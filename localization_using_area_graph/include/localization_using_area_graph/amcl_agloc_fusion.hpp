/**
 * @file amcl_agloc_fusion.hpp
 * <AUTHOR>
 * @brief AMCL与AGLoc融合的核心算法类
 * @version 1.0
 * @date 2025-01-20
 *
 * @details 该类实现了AMCL粒子滤波思想与AGLoc Area Graph定位的融合算法，
 *          主要功能包括：
 *          1. 多假设粒子管理和权重更新
 *          2. 运动模型预测和传感器模型更新
 *          3. 自适应重采样和全局定位恢复
 *          4. 与原有AGLoc系统的兼容性保持
 */

#pragma once
#ifndef _AMCL_AGLOC_FUSION_HPP_
#define _AMCL_AGLOC_FUSION_HPP_

#include "cloudBase.hpp"
#include <vector>
#include <random>
#include <memory>
#include <nav_msgs/msg/odometry.hpp>
#include <geometry_msgs/msg/pose_array.hpp>

namespace localization_using_area_graph {

/**
 * @class AMCLAGLocFusion
 * @brief AMCL与AGLoc融合算法的核心实现类
 *
 * 该类继承自CloudBase，保持与现有AGLoc系统的兼容性，
 * 同时引入AMCL的粒子滤波机制来解决几何对称性和跟踪稳定性问题。
 */
class AMCLAGLocFusion : public CloudBase {
public:
    /**
     * @brief 构造函数
     * @param node_name 节点名称
     */
    explicit AMCLAGLocFusion(const std::string& node_name = "amcl_agloc_fusion");

    /**
     * @brief 析构函数
     */
    ~AMCLAGLocFusion() override = default;

    // ==================== 核心算法接口 ====================

    /**
     * @brief 初始化粒子滤波器
     * @param initial_pose 初始位姿估计
     * @param initial_covariance 初始协方差矩阵
     */
    void initializeParticleFilter(const Eigen::Matrix4f& initial_pose,
                                 const Eigen::Matrix3d& initial_covariance);

    /**
     * @brief 全局定位模式：多假设粒子生成和评估
     * @param laser_cloud 激光点云数据
     * @param wifi_candidates WiFi提供的候选位置
     * @return 是否成功完成全局定位
     */
    bool globalLocalizationWithMultiHypothesis(
        pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud,
        const std::vector<Eigen::Vector3f>& wifi_candidates);

    /**
     * @brief 位姿跟踪模式：预测-更新-重采样循环
     * @param laser_cloud 激光点云数据
     * @param odometry 里程计数据（可选）
     * @return 更新后的最优位姿估计
     */
    Eigen::Matrix4f trackingWithParticleFilter(
        pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud,
        const nav_msgs::msg::Odometry::ConstSharedPtr& odometry = nullptr);

    // ==================== 运动模型 ====================

    /**
     * @brief 里程计运动模型预测
     * @param odometry_delta 里程计变化量
     */
    void motionModelUpdate(const Eigen::Vector3d& odometry_delta);

    /**
     * @brief 添加运动噪声到粒子
     * @param particle 输入粒子
     * @param delta 运动变化量
     * @return 添加噪声后的粒子
     */
    CloudBase::AGLocParticle addMotionNoise(const CloudBase::AGLocParticle& particle,
                                           const Eigen::Vector3d& delta);

    // ==================== 传感器模型 ====================

    /**
     * @brief 传感器模型权重更新
     * @param laser_cloud 激光点云数据
     */
    void sensorModelUpdate(pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud);

    /**
     * @brief 计算单个粒子的似然权重
     * @param particle 输入粒子
     * @param laser_cloud 激光点云数据
     * @return 粒子权重
     */
    double calculateParticleLikelihood(const CloudBase::AGLocParticle& particle,
                                      pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud);

    // ==================== 重采样算法 ====================

    /**
     * @brief 自适应重采样
     * @return 是否执行了重采样
     */
    bool adaptiveResample();

    /**
     * @brief 低方差重采样算法
     */
    void lowVarianceResample();

    /**
     * @brief 全局定位恢复：注入随机粒子
     * @param injection_ratio 注入比例
     */
    void injectRandomParticles(double injection_ratio);

    // ==================== 状态估计 ====================

    /**
     * @brief 获取最优位姿假设
     * @return 最优位姿矩阵
     */
    Eigen::Matrix4f getBestPoseEstimate();

    /**
     * @brief 获取位姿协方差
     * @return 位姿协方差矩阵
     */
    Eigen::Matrix3d getPoseCovariance();

    /**
     * @brief 检查粒子滤波器收敛性
     * @return 是否收敛
     */
    bool isConverged();

    /**
     * @brief 检查AMCL融合算法是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return is_initialized_; }

    // ==================== 配置和调试 ====================

    /**
     * @brief 设置AMCL-AGLoc配置参数
     * @param config 配置结构体
     */
    void setConfig(const AMCLAGLocConfig& config);

    /**
     * @brief 获取当前配置
     * @return 配置结构体
     */
    const AMCLAGLocConfig& getConfig() const { return config_; }

    /**
     * @brief 发布粒子云用于可视化
     */
    void publishParticleCloud();

    /**
     * @brief 获取粒子统计信息
     * @return 统计信息字符串
     */
    std::string getParticleStatistics();

    // ==================== 兼容性接口 ====================

    /**
     * @brief 兼容原有AGLoc接口：设置地图点云
     * @param map_pc 地图点云
     */
    void setMapPC(pcl::PointCloud<pcl::PointXYZI>::Ptr map_pc) override;

    /**
     * @brief 兼容原有AGLoc接口：重置参数
     */
    void resetParameters() override;

    // ==================== 实现CloudBase纯虚函数 ====================

    /**
     * @brief 实现CloudBase纯虚函数：计算最近地图点
     * @param inside_index 内部索引
     */
    void calClosestMapPoint(int inside_index) override;

    /**
     * @brief 实现CloudBase纯虚函数：检查地图
     * @param ring 环索引
     * @param horizonIndex 水平索引
     * @param last_index 最后索引
     * @param minDist 最小距离
     * @param inside_index 内部索引
     * @return 是否成功
     */
    bool checkMap(int ring, int horizonIndex, int& last_index, double& minDist, int inside_index) override;

    /**
     * @brief 实现CloudBase纯虚函数：分配内存
     */
    void allocateMemory() override;

private:
    // ==================== 私有辅助方法 ====================

    /**
     * @brief 从参数服务器加载配置
     */
    void loadConfigFromParameters();

    /**
     * @brief 计算里程计变化量
     * @param current_odom 当前里程计数据
     * @return 变化量 [dx, dy, dyaw]
     */
    Eigen::Vector3d calculateOdometryDelta(const nav_msgs::msg::Odometry::ConstSharedPtr& current_odom);

    /**
     * @brief 使用ICP精化位姿
     * @param initial_pose 初始位姿
     * @param laser_cloud 激光点云
     * @return 精化后的位姿
     */
    Eigen::Matrix4f refineWithICP(const Eigen::Matrix4f& initial_pose,
                                 pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud);

    /**
     * @brief 更新性能统计
     * @param elapsed_time 处理时间(毫秒)
     */
    void updatePerformanceStatistics(double elapsed_time);

    /**
     * @brief 计算点到线段的距离（AGLoc特化）
     * @param px 点的x坐标
     * @param py 点的y坐标
     * @return 最小距离
     */
    double calculatePointToLineDistance(double px, double py);

protected:
    // ==================== 内部数据成员 ====================

    // 粒子集合
    std::vector<CloudBase::AGLocParticle> particles_;
    std::vector<CloudBase::AGLocParticle> particles_backup_;  // 备份粒子集合

    // 配置参数
    AMCLAGLocConfig config_;

    // 状态变量
    bool is_initialized_;
    bool is_global_localization_mode_;
    int resample_count_;
    double w_slow_, w_fast_;  // 权重运行平均

    // 随机数生成器
    std::mt19937 rng_;
    std::normal_distribution<double> normal_dist_;
    std::uniform_real_distribution<double> uniform_dist_;

    // ROS2发布器
    rclcpp::Publisher<geometry_msgs::msg::PoseArray>::SharedPtr particle_pub_;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr best_pose_pub_;

    // 里程计相关
    nav_msgs::msg::Odometry last_odometry_;
    bool has_odometry_;

    // 性能统计
    std::chrono::steady_clock::time_point last_update_time_;
    double average_update_time_;
    int update_count_;

    // ==================== 内部辅助方法 ====================

    /**
     * @brief 归一化粒子权重
     */
    void normalizeWeights();

    /**
     * @brief 计算有效粒子数
     * @return 有效粒子数
     */
    double calculateEffectiveParticleCount();

    /**
     * @brief 更新权重运行平均
     */
    void updateWeightAverages();

    /**
     * @brief 从位姿矩阵提取欧拉角
     * @param pose 位姿矩阵
     * @return [x, y, yaw]
     */
    Eigen::Vector3d extractPoseVector(const Eigen::Matrix4f& pose);

    /**
     * @brief 从欧拉角构造位姿矩阵
     * @param pose_vec [x, y, yaw]
     * @return 位姿矩阵
     */
    Eigen::Matrix4f constructPoseMatrix(const Eigen::Vector3d& pose_vec);

    /**
     * @brief 生成高斯随机数
     * @param mean 均值
     * @param std 标准差
     * @return 随机数
     */
    double sampleGaussian(double mean, double std);

    /**
     * @brief 生成均匀随机数
     * @param min 最小值
     * @param max 最大值
     * @return 随机数
     */
    double sampleUniform(double min, double max);
};

} // namespace localization_using_area_graph

#endif // _AMCL_AGLOC_FUSION_HPP_
