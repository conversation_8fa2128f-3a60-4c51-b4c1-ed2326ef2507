/**
 * @file amcl_agloc_fusion.cpp
 * <AUTHOR>
 * @brief AMCL与AGLoc融合算法的核心实现
 * @version 1.0
 * @date 2025-01-20
 */

#include "localization_using_area_graph/amcl_agloc_fusion.hpp"
#include <algorithm>
#include <numeric>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <chrono>

namespace localization_using_area_graph {

AMCLAGLocFusion::AMCLAGLocFusion(const std::string& node_name)
    : CloudBase(node_name),
      is_initialized_(false),
      is_global_localization_mode_(true),
      resample_count_(0),
      w_slow_(0.0),
      w_fast_(0.0),
      rng_(std::random_device{}()),
      normal_dist_(0.0, 1.0),
      uniform_dist_(0.0, 1.0),
      has_odometry_(false),
      average_update_time_(0.0),
      update_count_(0)
{
    RCLCPP_INFO(get_logger(), "初始化AMCL-AGLoc融合算法");

    // 初始化ROS2发布器
    particle_pub_ = create_publisher<geometry_msgs::msg::PoseArray>(
        "/amcl_agloc/particle_cloud", 10);
    best_pose_pub_ = create_publisher<geometry_msgs::msg::PoseStamped>(
        "/amcl_agloc/best_pose", 10);

    // 注意：AMCL相关参数已在ParamServer中声明，这里不需要重复声明
    // 参数将通过loadConfigFromParameters()从父节点获取

    // 加载配置参数
    loadConfigFromParameters();
}

void AMCLAGLocFusion::loadConfigFromParameters() {
    // 检查参数是否已声明，如果没有则使用默认值
    if (has_parameter("enable_amcl_mode")) {
        config_.enable_amcl_mode = get_parameter("enable_amcl_mode").as_bool();
    } else {
        config_.enable_amcl_mode = false;
        RCLCPP_WARN(get_logger(), "参数 enable_amcl_mode 未声明，使用默认值: false");
    }
    // 为所有参数添加安全检查
    config_.use_odometry_prediction = has_parameter("use_odometry_prediction") ?
        get_parameter("use_odometry_prediction").as_bool() : false;
    config_.min_particles = has_parameter("amcl_min_particles") ?
        get_parameter("amcl_min_particles").as_int() : 100;
    config_.max_particles = has_parameter("amcl_max_particles") ?
        get_parameter("amcl_max_particles").as_int() : 5000;
    config_.alpha_slow = has_parameter("amcl_alpha_slow") ?
        get_parameter("amcl_alpha_slow").as_double() : 0.001;
    config_.alpha_fast = has_parameter("amcl_alpha_fast") ?
        get_parameter("amcl_alpha_fast").as_double() : 0.1;
    config_.resample_interval = has_parameter("amcl_resample_interval") ?
        get_parameter("amcl_resample_interval").as_int() : 2;
    config_.alpha1 = has_parameter("amcl_alpha1") ?
        get_parameter("amcl_alpha1").as_double() : 0.2;
    config_.alpha2 = has_parameter("amcl_alpha2") ?
        get_parameter("amcl_alpha2").as_double() : 0.2;
    config_.alpha3 = has_parameter("amcl_alpha3") ?
        get_parameter("amcl_alpha3").as_double() : 0.2;
    config_.alpha4 = has_parameter("amcl_alpha4") ?
        get_parameter("amcl_alpha4").as_double() : 0.2;
    config_.translation_threshold = has_parameter("amcl_translation_threshold") ?
        get_parameter("amcl_translation_threshold").as_double() : 0.2;
    config_.rotation_threshold = has_parameter("amcl_rotation_threshold") ?
        get_parameter("amcl_rotation_threshold").as_double() : 0.2;
    config_.z_hit = has_parameter("amcl_z_hit") ?
        get_parameter("amcl_z_hit").as_double() : 0.95;
    config_.z_rand = has_parameter("amcl_z_rand") ?
        get_parameter("amcl_z_rand").as_double() : 0.05;
    config_.sigma_hit = has_parameter("amcl_sigma_hit") ?
        get_parameter("amcl_sigma_hit").as_double() : 0.2;
    config_.area_transition_threshold = has_parameter("amcl_area_transition_threshold") ?
        get_parameter("amcl_area_transition_threshold").as_double() : 1.0;

    RCLCPP_INFO(get_logger(), "AMCL模式: %s, 粒子数范围: [%d, %d]",
                config_.enable_amcl_mode ? "启用" : "禁用",
                config_.min_particles, config_.max_particles);
}

void AMCLAGLocFusion::initializeParticleFilter(const Eigen::Matrix4f& initial_pose,
                                               const Eigen::Matrix3d& initial_covariance) {
    if (!config_.enable_amcl_mode) {
        RCLCPP_INFO(get_logger(), "AMCL模式未启用，跳过粒子滤波器初始化");
        return;
    }

    RCLCPP_INFO(get_logger(), "初始化粒子滤波器，粒子数: %d", config_.max_particles);

    particles_.clear();
    particles_.reserve(config_.max_particles);

    // 从初始位姿和协方差生成粒子
    Eigen::Vector3d initial_pose_vec = extractPoseVector(initial_pose);

    for (int i = 0; i < config_.max_particles; ++i) {
        AGLocParticle particle;

        // 从高斯分布采样位姿
        double x = initial_pose_vec[0] + sampleGaussian(0.0, sqrt(initial_covariance(0,0)));
        double y = initial_pose_vec[1] + sampleGaussian(0.0, sqrt(initial_covariance(1,1)));
        double yaw = initial_pose_vec[2] + sampleGaussian(0.0, sqrt(initial_covariance(2,2)));

        particle.pose = constructPoseMatrix(Eigen::Vector3d(x, y, yaw));
        particle.weight = 1.0 / config_.max_particles;
        particle.is_valid = true;

        particles_.push_back(particle);
    }

    w_slow_ = w_fast_ = 0.0;
    resample_count_ = 0;
    is_initialized_ = true;

    RCLCPP_INFO(get_logger(), "粒子滤波器初始化完成");
}

bool AMCLAGLocFusion::globalLocalizationWithMultiHypothesis(
    pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud,
    const std::vector<Eigen::Vector3f>& wifi_candidates) {

    if (!config_.enable_amcl_mode) {
        // 回退到原有的全局定位算法
        return false;
    }

    RCLCPP_INFO(get_logger(), "开始AMCL多假设全局定位，WiFi候选数: %zu", wifi_candidates.size());

    auto start_time = std::chrono::steady_clock::now();

    // 1. 基于WiFi候选生成多假设粒子
    particles_.clear();
    int particles_per_candidate = config_.max_particles / std::max(1, (int)wifi_candidates.size());

    for (const auto& candidate : wifi_candidates) {
        for (int i = 0; i < particles_per_candidate; ++i) {
            AGLocParticle particle;

            // 在候选位置周围采样
            double x = candidate[0] + sampleGaussian(0.0, 2.0);  // 2米标准差
            double y = candidate[1] + sampleGaussian(0.0, 2.0);
            double yaw = sampleUniform(0.0, 2.0 * M_PI);

            particle.pose = constructPoseMatrix(Eigen::Vector3d(x, y, yaw));
            particle.weight = 1.0 / config_.max_particles;
            particle.is_valid = true;

            particles_.push_back(particle);
        }
    }

    // 2. 使用传感器模型更新粒子权重
    sensorModelUpdate(laser_cloud);

    // 3. 检查是否有足够好的假设
    normalizeWeights();
    auto best_particle = std::max_element(particles_.begin(), particles_.end(),
        [](const AGLocParticle& a, const AGLocParticle& b) {
            return a.weight < b.weight;
        });

    if (best_particle != particles_.end() && best_particle->weight > 0.1) {
        // 找到可信的全局位姿，切换到跟踪模式
        is_global_localization_mode_ = false;

        auto end_time = std::chrono::steady_clock::now();
        double elapsed_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();

        RCLCPP_INFO(get_logger(), "全局定位成功，最佳权重: %.6f, 耗时: %.2f ms",
                    best_particle->weight, elapsed_ms);

        return true;
    }

    RCLCPP_WARN(get_logger(), "全局定位未收敛，最佳权重: %.6f",
                best_particle != particles_.end() ? best_particle->weight : 0.0);
    return false;
}

Eigen::Matrix4f AMCLAGLocFusion::trackingWithParticleFilter(
    pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud,
    const nav_msgs::msg::Odometry::ConstSharedPtr& odometry) {

    if (!config_.enable_amcl_mode || !is_initialized_) {
        // 回退到原有的ICP跟踪算法
        return robotPose;
    }

    auto start_time = std::chrono::steady_clock::now();

    // 1. 运动模型预测（如果有里程计数据）
    if (config_.use_odometry_prediction && odometry && has_odometry_) {
        Eigen::Vector3d delta = calculateOdometryDelta(odometry);
        motionModelUpdate(delta);
    }

    // 2. 传感器模型更新
    sensorModelUpdate(laser_cloud);

    // 3. 重采样（如果需要）
    bool resampled = false;
    if (++resample_count_ % config_.resample_interval == 0) {
        resampled = adaptiveResample();
    }

    // 4. 获取最优位姿估计
    Eigen::Matrix4f best_pose = getBestPoseEstimate();

    // 5. 可选的ICP精化
    if (config_.icp_refinement_enabled && resampled) {
        best_pose = refineWithICP(best_pose, laser_cloud);
    }

    // 6. 更新统计信息
    auto end_time = std::chrono::steady_clock::now();
    double elapsed_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    updatePerformanceStatistics(elapsed_ms);

    // 7. 发布可视化信息
    if (config_.publish_particles) {
        publishParticleCloud();
    }

    // 更新里程计历史
    if (odometry) {
        last_odometry_ = *odometry;
        has_odometry_ = true;
    }

    return best_pose;
}

void AMCLAGLocFusion::motionModelUpdate(const Eigen::Vector3d& odometry_delta) {
    // 为每个粒子添加运动噪声
    for (auto& particle : particles_) {
        if (!particle.is_valid) continue;

        particle = addMotionNoise(particle, odometry_delta);
    }
}

CloudBase::AGLocParticle AMCLAGLocFusion::addMotionNoise(const CloudBase::AGLocParticle& particle,
                                             const Eigen::Vector3d& delta) {
    CloudBase::AGLocParticle noisy_particle = particle;

    // 提取当前位姿
    Eigen::Vector3d current_pose = extractPoseVector(particle.pose);

    // 计算运动参数
    double delta_trans = sqrt(delta[0] * delta[0] + delta[1] * delta[1]);
    double delta_rot1 = atan2(delta[1], delta[0]) - current_pose[2];
    double delta_rot2 = delta[2] - delta_rot1;

    // 添加噪声（基于AMCL的运动模型）
    double delta_rot1_noise = std::abs(delta_rot1);
    double delta_rot2_noise = std::abs(delta_rot2);

    double delta_rot1_hat = delta_rot1 - sampleGaussian(0.0,
        sqrt(config_.alpha1 * delta_rot1_noise + config_.alpha2 * delta_trans));
    double delta_trans_hat = delta_trans - sampleGaussian(0.0,
        sqrt(config_.alpha3 * delta_trans + config_.alpha4 * (delta_rot1_noise + delta_rot2_noise)));
    double delta_rot2_hat = delta_rot2 - sampleGaussian(0.0,
        sqrt(config_.alpha1 * delta_rot2_noise + config_.alpha2 * delta_trans));

    // 应用运动更新
    double new_x = current_pose[0] + delta_trans_hat * cos(current_pose[2] + delta_rot1_hat);
    double new_y = current_pose[1] + delta_trans_hat * sin(current_pose[2] + delta_rot1_hat);
    double new_yaw = current_pose[2] + delta_rot1_hat + delta_rot2_hat;

    noisy_particle.pose = constructPoseMatrix(Eigen::Vector3d(new_x, new_y, new_yaw));

    return noisy_particle;
}

void AMCLAGLocFusion::sensorModelUpdate(pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud) {
    // 并行计算每个粒子的似然权重
    #pragma omp parallel for
    for (size_t i = 0; i < particles_.size(); ++i) {
        if (!particles_[i].is_valid) continue;

        particles_[i].weight = calculateParticleLikelihood(particles_[i], laser_cloud);
    }

    // 归一化权重
    normalizeWeights();

    // 更新权重运行平均
    updateWeightAverages();
}

double AMCLAGLocFusion::calculateParticleLikelihood(const CloudBase::AGLocParticle& particle,
                                                   pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud) {
    // 使用AGLoc的点线匹配机制计算似然
    // 这里复用现有的calClosestMapPoint逻辑，但适配粒子滤波框架

    // 变换点云到粒子位姿
    auto transformed_cloud = std::make_shared<pcl::PointCloud<pcl::PointXYZI>>();
    pcl::transformPointCloud(*laser_cloud, *transformed_cloud, particle.pose);

    double total_likelihood = 0.0;
    int valid_points = 0;

    // 对每个激光点计算似然
    for (const auto& point : transformed_cloud->points) {
        if (point.x == 0 && point.y == 0) continue;  // 跳过无效点

        // 找到最近的地图线段
        double min_distance = std::numeric_limits<double>::max();
        bool found_match = false;

        // 使用AGLoc的点线匹配算法计算距离
        if (map_pc_ && !map_pc_->points.empty()) {
            // 调用AGLoc的点线匹配函数
            double map1x, map1y, map2x, map2y, intersectionx, intersectiony;

            // 这里需要访问CloudHandler的checkWholeMap方法
            // 为了简化，我们使用改进的距离计算，考虑线段匹配
            min_distance = calculatePointToLineDistance(point.x, point.y);
            found_match = (min_distance < config_.likelihood_max_dist);
        }

        if (found_match) {
            // 使用高斯似然模型
            double likelihood = config_.z_hit * exp(-0.5 * pow(min_distance / config_.sigma_hit, 2)) +
                               config_.z_rand / config_.likelihood_max_dist;
            total_likelihood += log(likelihood);
            valid_points++;
        }
    }

    return valid_points > 0 ? exp(total_likelihood / valid_points) : 1e-6;
}

bool AMCLAGLocFusion::adaptiveResample() {
    double effective_particles = calculateEffectiveParticleCount();
    double threshold = config_.resample_threshold * particles_.size();

    if (effective_particles < threshold) {
        RCLCPP_DEBUG(get_logger(), "执行重采样，有效粒子数: %.1f < %.1f",
                     effective_particles, threshold);

        lowVarianceResample();

        // 检查是否需要全局定位恢复
        double w_diff = 1.0 - w_fast_ / w_slow_;
        if (w_diff > 0.0) {
            injectRandomParticles(w_diff * config_.random_injection_ratio);
        }

        return true;
    }

    return false;
}

void AMCLAGLocFusion::lowVarianceResample() {
    std::vector<AGLocParticle> new_particles;
    new_particles.reserve(particles_.size());

    // 构建累积权重表
    std::vector<double> cumulative_weights(particles_.size());
    cumulative_weights[0] = particles_[0].weight;
    for (size_t i = 1; i < particles_.size(); ++i) {
        cumulative_weights[i] = cumulative_weights[i-1] + particles_[i].weight;
    }

    // 低方差重采样
    double r = sampleUniform(0.0, 1.0 / particles_.size());
    size_t i = 0;

    for (size_t m = 0; m < particles_.size(); ++m) {
        double u = r + m * (1.0 / particles_.size());
        while (u > cumulative_weights[i] && i < particles_.size() - 1) {
            i++;
        }

        AGLocParticle new_particle = particles_[i];
        new_particle.weight = 1.0 / particles_.size();
        new_particles.push_back(new_particle);
    }

    particles_ = std::move(new_particles);
}

void AMCLAGLocFusion::injectRandomParticles(double injection_ratio) {
    int num_random = static_cast<int>(injection_ratio * particles_.size());

    RCLCPP_DEBUG(get_logger(), "注入 %d 个随机粒子用于全局定位恢复", num_random);

    // 替换权重最低的粒子
    std::partial_sort(particles_.begin(), particles_.begin() + num_random, particles_.end(),
        [](const AGLocParticle& a, const AGLocParticle& b) {
            return a.weight < b.weight;
        });

    for (int i = 0; i < num_random; ++i) {
        // 在地图范围内随机生成粒子
        // 这里需要根据实际地图边界来设置
        double x = sampleUniform(-50.0, 50.0);  // 假设地图范围
        double y = sampleUniform(-50.0, 50.0);
        double yaw = sampleUniform(0.0, 2.0 * M_PI);

        particles_[i].pose = constructPoseMatrix(Eigen::Vector3d(x, y, yaw));
        particles_[i].weight = 1.0 / particles_.size();
        particles_[i].is_valid = true;
    }
}

Eigen::Matrix4f AMCLAGLocFusion::getBestPoseEstimate() {
    if (particles_.empty()) {
        RCLCPP_WARN(get_logger(), "粒子集合为空，返回单位矩阵");
        return Eigen::Matrix4f::Identity();
    }

    // 找到权重最高的粒子
    auto best_particle = std::max_element(particles_.begin(), particles_.end(),
        [](const AGLocParticle& a, const AGLocParticle& b) {
            return a.weight < b.weight;
        });

    if (best_particle == particles_.end()) {
        RCLCPP_ERROR(get_logger(), "无法找到最佳粒子，返回单位矩阵");
        return Eigen::Matrix4f::Identity();
    }

    // 验证最佳粒子的位姿有效性
    Eigen::Matrix4f best_pose = best_particle->pose;
    double x = best_pose(0, 3);
    double y = best_pose(1, 3);

    // 检查位姿是否有效
    bool pose_valid = !std::isnan(x) && !std::isnan(y) &&
                      std::abs(x) < 1000.0 && std::abs(y) < 1000.0 &&
                      best_particle->weight > 1e-10;

    if (!pose_valid) {
        RCLCPP_WARN(get_logger(), "最佳粒子位姿无效: [%.2f, %.2f], 权重: %.6f，返回单位矩阵",
                    x, y, best_particle->weight);
        return Eigen::Matrix4f::Identity();
    }

    RCLCPP_DEBUG(get_logger(), "最佳粒子位姿: [%.2f, %.2f], 权重: %.6f",
                 x, y, best_particle->weight);

    return best_pose;
}

void AMCLAGLocFusion::normalizeWeights() {
    double total_weight = 0.0;
    for (const auto& particle : particles_) {
        total_weight += particle.weight;
    }

    if (total_weight > 0.0) {
        for (auto& particle : particles_) {
            particle.weight /= total_weight;
        }
    } else {
        // 如果所有权重都为0，重置为均匀分布
        double uniform_weight = 1.0 / particles_.size();
        for (auto& particle : particles_) {
            particle.weight = uniform_weight;
        }
    }
}

double AMCLAGLocFusion::calculateEffectiveParticleCount() {
    double sum_squared_weights = 0.0;
    for (const auto& particle : particles_) {
        sum_squared_weights += particle.weight * particle.weight;
    }
    return sum_squared_weights > 0.0 ? 1.0 / sum_squared_weights : 0.0;
}

void AMCLAGLocFusion::updateWeightAverages() {
    double weight_avg = 0.0;
    for (const auto& particle : particles_) {
        weight_avg += particle.weight;
    }
    weight_avg /= particles_.size();

    if (w_slow_ == 0.0) {
        w_slow_ = weight_avg;
    } else {
        w_slow_ += config_.alpha_slow * (weight_avg - w_slow_);
    }

    if (w_fast_ == 0.0) {
        w_fast_ = weight_avg;
    } else {
        w_fast_ += config_.alpha_fast * (weight_avg - w_fast_);
    }
}

Eigen::Vector3d AMCLAGLocFusion::extractPoseVector(const Eigen::Matrix4f& pose) {
    double x = pose(0, 3);
    double y = pose(1, 3);
    double yaw = atan2(pose(1, 0), pose(0, 0));
    return Eigen::Vector3d(x, y, yaw);
}

Eigen::Matrix4f AMCLAGLocFusion::constructPoseMatrix(const Eigen::Vector3d& pose_vec) {
    Eigen::Matrix4f pose = Eigen::Matrix4f::Identity();
    pose(0, 3) = pose_vec[0];
    pose(1, 3) = pose_vec[1];
    pose(0, 0) = cos(pose_vec[2]);
    pose(0, 1) = -sin(pose_vec[2]);
    pose(1, 0) = sin(pose_vec[2]);
    pose(1, 1) = cos(pose_vec[2]);
    return pose;
}

double AMCLAGLocFusion::sampleGaussian(double mean, double std) {
    return mean + std * normal_dist_(rng_);
}

double AMCLAGLocFusion::sampleUniform(double min, double max) {
    return min + (max - min) * uniform_dist_(rng_);
}

void AMCLAGLocFusion::publishParticleCloud() {
    if (!config_.publish_particles || particles_.empty()) return;

    auto particle_array = std::make_unique<geometry_msgs::msg::PoseArray>();
    particle_array->header.stamp = this->now();
    particle_array->header.frame_id = "map";

    for (const auto& particle : particles_) {
        if (!particle.is_valid) continue;

        geometry_msgs::msg::Pose pose_msg;
        Eigen::Vector3d pose_vec = extractPoseVector(particle.pose);

        pose_msg.position.x = pose_vec[0];
        pose_msg.position.y = pose_vec[1];
        pose_msg.position.z = 0.0;

        // 转换yaw角为四元数
        double yaw = pose_vec[2];
        pose_msg.orientation.z = sin(yaw / 2.0);
        pose_msg.orientation.w = cos(yaw / 2.0);

        particle_array->poses.push_back(pose_msg);
    }

    particle_pub_->publish(std::move(particle_array));
}

void AMCLAGLocFusion::setMapPC(pcl::PointCloud<pcl::PointXYZI>::Ptr map_pc) {
    map_pc_ = map_pc;
    RCLCPP_INFO(get_logger(), "AMCL-AGLoc融合算法已更新地图点云");
}

void AMCLAGLocFusion::resetParameters() {
    CloudBase::resetParameters();

    if (config_.enable_amcl_mode) {
        particles_.clear();
        is_initialized_ = false;
        is_global_localization_mode_ = true;
        resample_count_ = 0;
        w_slow_ = w_fast_ = 0.0;

        RCLCPP_INFO(get_logger(), "AMCL-AGLoc融合算法参数已重置");
    }
}

Eigen::Vector3d AMCLAGLocFusion::calculateOdometryDelta(const nav_msgs::msg::Odometry::ConstSharedPtr& current_odom) {
    if (!has_odometry_) {
        return Eigen::Vector3d::Zero();
    }

    // 提取当前位姿
    double curr_x = current_odom->pose.pose.position.x;
    double curr_y = current_odom->pose.pose.position.y;

    // 从四元数提取yaw角
    double curr_qx = current_odom->pose.pose.orientation.x;
    double curr_qy = current_odom->pose.pose.orientation.y;
    double curr_qz = current_odom->pose.pose.orientation.z;
    double curr_qw = current_odom->pose.pose.orientation.w;
    double curr_yaw = atan2(2.0 * (curr_qw * curr_qz + curr_qx * curr_qy),
                           1.0 - 2.0 * (curr_qy * curr_qy + curr_qz * curr_qz));

    // 提取上一次位姿
    double last_x = last_odometry_.pose.pose.position.x;
    double last_y = last_odometry_.pose.pose.position.y;

    double last_qx = last_odometry_.pose.pose.orientation.x;
    double last_qy = last_odometry_.pose.pose.orientation.y;
    double last_qz = last_odometry_.pose.pose.orientation.z;
    double last_qw = last_odometry_.pose.pose.orientation.w;
    double last_yaw = atan2(2.0 * (last_qw * last_qz + last_qx * last_qy),
                           1.0 - 2.0 * (last_qy * last_qy + last_qz * last_qz));

    // 计算变化量
    double dx = curr_x - last_x;
    double dy = curr_y - last_y;
    double dyaw = curr_yaw - last_yaw;

    // 处理角度跳跃
    while (dyaw > M_PI) dyaw -= 2.0 * M_PI;
    while (dyaw < -M_PI) dyaw += 2.0 * M_PI;

    return Eigen::Vector3d(dx, dy, dyaw);
}

Eigen::Matrix4f AMCLAGLocFusion::refineWithICP(const Eigen::Matrix4f& initial_pose,
                                              pcl::PointCloud<pcl::PointXYZI>::Ptr laser_cloud) {
    // 这里可以复用现有的ICP实现
    // 为了简化，我们返回原始位姿
    // 在实际实现中，应该调用现有的optimizationICP()方法

    RCLCPP_DEBUG(get_logger(), "使用ICP精化最优粒子位姿");

    // 设置临时的robotPose用于ICP
    Eigen::Matrix4f original_pose = robotPose;
    robotPose = initial_pose;

    // 变换点云
    auto transformed_cloud = std::make_shared<pcl::PointCloud<pcl::PointXYZI>>();
    pcl::transformPointCloud(*laser_cloud, *transformed_cloud, robotPose);

    // 这里应该调用现有的ICP优化逻辑
    // 由于复杂性，我们暂时返回原始位姿
    Eigen::Matrix4f refined_pose = robotPose;

    // 恢复原始位姿
    robotPose = original_pose;

    return refined_pose;
}

void AMCLAGLocFusion::updatePerformanceStatistics(double elapsed_time) {
    update_count_++;

    if (update_count_ == 1) {
        average_update_time_ = elapsed_time;
    } else {
        // 指数移动平均
        double alpha = 0.1;
        average_update_time_ = alpha * elapsed_time + (1.0 - alpha) * average_update_time_;
    }

    if (update_count_ % 100 == 0) {
        RCLCPP_INFO(get_logger(), "AMCL-AGLoc性能统计: 平均处理时间 %.2f ms, 更新次数 %d",
                    average_update_time_, update_count_);
    }
}

std::string AMCLAGLocFusion::getParticleStatistics() {
    if (particles_.empty()) {
        return "粒子统计: 无粒子";
    }

    double max_weight = 0.0;
    double min_weight = std::numeric_limits<double>::max();
    double weight_sum = 0.0;
    int valid_particles = 0;

    for (const auto& particle : particles_) {
        if (particle.is_valid) {
            max_weight = std::max(max_weight, particle.weight);
            min_weight = std::min(min_weight, particle.weight);
            weight_sum += particle.weight;
            valid_particles++;
        }
    }

    double avg_weight = valid_particles > 0 ? weight_sum / valid_particles : 0.0;
    double effective_particles = calculateEffectiveParticleCount();

    std::stringstream ss;
    ss << "粒子统计: 总数=" << particles_.size()
       << ", 有效=" << valid_particles
       << ", 权重[最小=" << std::fixed << std::setprecision(6) << min_weight
       << ", 平均=" << avg_weight
       << ", 最大=" << max_weight << "]"
       << ", 有效粒子数=" << std::setprecision(1) << effective_particles;

    return ss.str();
}

Eigen::Matrix3d AMCLAGLocFusion::getPoseCovariance() {
    if (particles_.empty()) {
        return Eigen::Matrix3d::Identity();
    }

    // 计算加权均值
    Eigen::Vector3d weighted_mean = Eigen::Vector3d::Zero();
    double total_weight = 0.0;

    for (const auto& particle : particles_) {
        if (particle.is_valid) {
            Eigen::Vector3d pose_vec = extractPoseVector(particle.pose);
            weighted_mean += particle.weight * pose_vec;
            total_weight += particle.weight;
        }
    }

    if (total_weight > 0.0) {
        weighted_mean /= total_weight;
    }

    // 计算协方差矩阵
    Eigen::Matrix3d covariance = Eigen::Matrix3d::Zero();

    for (const auto& particle : particles_) {
        if (particle.is_valid) {
            Eigen::Vector3d pose_vec = extractPoseVector(particle.pose);
            Eigen::Vector3d diff = pose_vec - weighted_mean;

            // 处理角度差异
            while (diff[2] > M_PI) diff[2] -= 2.0 * M_PI;
            while (diff[2] < -M_PI) diff[2] += 2.0 * M_PI;

            covariance += particle.weight * (diff * diff.transpose());
        }
    }

    if (total_weight > 0.0) {
        covariance /= total_weight;
    }

    return covariance;
}

bool AMCLAGLocFusion::isConverged() {
    if (particles_.empty()) {
        return false;
    }

    // 检查粒子分布的收敛性
    Eigen::Matrix3d covariance = getPoseCovariance();

    // 计算位置和角度的标准差
    double pos_std = sqrt(covariance(0,0) + covariance(1,1));
    double angle_std = sqrt(covariance(2,2));

    // 收敛条件：位置标准差 < 阈值 且 角度标准差 < 阈值
    bool position_converged = pos_std < config_.particle_spread_threshold;
    bool angle_converged = angle_std < (M_PI / 6.0);  // 30度

    return position_converged && angle_converged;
}

void AMCLAGLocFusion::setConfig(const AMCLAGLocConfig& config) {
    config_ = config;
    RCLCPP_INFO(get_logger(), "AMCL-AGLoc配置已更新");
}



// ==================== 实现CloudBase纯虚函数 ====================

void AMCLAGLocFusion::calClosestMapPoint(int inside_index) {
    // AMCL-AGLoc融合算法不需要实现这个方法，因为它使用自己的粒子滤波机制
    // 这里提供一个空实现以满足接口要求
    RCLCPP_DEBUG(get_logger(), "calClosestMapPoint called with inside_index: %d", inside_index);
}

bool AMCLAGLocFusion::checkMap(int ring, int horizonIndex, int& last_index, double& minDist, int inside_index) {
    // AMCL-AGLoc融合算法不需要实现这个方法，因为它使用自己的粒子滤波机制
    // 这里提供一个空实现以满足接口要求
    RCLCPP_DEBUG(get_logger(), "checkMap called with ring: %d, horizonIndex: %d, inside_index: %d",
                 ring, horizonIndex, inside_index);

    last_index = 0;
    minDist = 0.0;
    return false;
}

double AMCLAGLocFusion::calculatePointToLineDistance(double px, double py) {
    // 改进的点到线距离计算，考虑AGLoc的线段表示
    double min_distance = std::numeric_limits<double>::max();

    if (!map_pc_ || map_pc_->points.empty()) {
        return min_distance;
    }

    // 简化的线段匹配：将相邻的地图点视为线段
    for (size_t i = 0; i < map_pc_->points.size() - 1; ++i) {
        const auto& p1 = map_pc_->points[i];
        const auto& p2 = map_pc_->points[i + 1];

        // 计算点到线段的距离
        double A = py - p1.y;
        double B = px - p1.x;
        double C = p2.y - p1.y;
        double D = p2.x - p1.x;

        double dot = A * C + B * D;
        double len_sq = C * C + D * D;

        if (len_sq < 1e-6) continue;  // 避免除零

        double param = dot / len_sq;

        double xx, yy;
        if (param < 0) {
            xx = p1.x;
            yy = p1.y;
        } else if (param > 1) {
            xx = p2.x;
            yy = p2.y;
        } else {
            xx = p1.x + param * D;
            yy = p1.y + param * C;
        }

        double dx = px - xx;
        double dy = py - yy;
        double distance = sqrt(dx * dx + dy * dy);

        min_distance = std::min(min_distance, distance);
    }

    return min_distance;
}

void AMCLAGLocFusion::allocateMemory() {
    // AMCL-AGLoc融合算法的内存分配在构造函数和初始化方法中完成
    // 这里提供一个空实现以满足接口要求
    RCLCPP_DEBUG(get_logger(), "allocateMemory called for AMCL-AGLoc fusion");
}

} // namespace localization_using_area_graph
