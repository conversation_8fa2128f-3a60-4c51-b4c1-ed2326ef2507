/**:
    ros__parameters:
        # LiDAR配置参数
        # 这些参数主要在cloudBase.cpp的organizePointcloud()函数中使用，用于:
        # 点云数据的预处理和组织
        # 过滤无效点
        # 实现论文中的"clutter removal"步骤
        # pointCloudTopic: "/lidar_points"            # Point cloud topic  --- Agile03: /lidar_points ; Agile01,02: /hesai/pandar
        pointCloudTopic: "/hesai/pandar"            # 用agile-01录的包中的雷达话题名即为/hesai/pandar，以后用agile-03录的包，这里也不用改了，因为在launch播包时已经重映射了
        N_SCAN: 64                                  # 雷达垂直线数 number of lidar channel (i.e., 16, 32, 64, 128)
        Horizon_SCAN: 600 #1800                     # 水平分辨率 lidar horizontal resolution (Velodyne:1800, Ouster:512,1024,2048)
        downsampleRate: 1                           # 垂直方向降采样率 default: 1. Downsample your data if too many points. i.e., 16 = 64 / 4, 16 = 16 / 1
        downsampleRateHorizontal: 1                 # 水平方向降采样率
        lidarMinRange: 0.1                          # 最小有效距离 default: 1.0, minimum lidar range to be used
        lidarMaxRange: 1000.0                       # 最大有效距离 default: 1000.0, maximum lidar range to be used
        N_ceiling: 28                               # 天花板点云过滤阈值 from up to down, which beam may hit ceiling, used to delete ceiling points
        # ----------------------------------------------------

        # 全局定位参数
        # 这些参数实现了论文III.D节"Guess Scoring"中的误差阈值：
            # 在cloudInitializer.cpp的rescueRobot()函数中用于全局定位
            # 在cloudHandler.cpp的filterUsefulPoints()中用于pose tracking
            # 较大的Init阈值用于处理较大的初始误差
            # 较小的跟踪阈值用于精确定位

        # this is for initialization only, when with big initial error
        errorUpThredInit: 9.0                       # 全局定位时，外部点的阈值
        errorLowThredInit: 1.0                      # 全局定位时，内部点的阈值
        # do not set too big, since robot can see through doors.
        errorUpThred: 1.0 #5 #1.2 #1.8 if outside, bigger  # 跟踪定位时外部点阈值
        errorLowThred: 0.8 #1.0 #1.2                       # 跟踪定位时内部点阈值
        # -----------------------------------------------------

        # 外参和初始姿态
        # 这些参数在cloudBase.cpp的以下函数中使用：
            # setInitialPose()：设置机器人初始姿态
            # mapAGCB()：处理地图坐标变换

        # lidar to map
        # # 0510 bag
        # mapExtrinsicTrans: [9.8,-34.3, 0.0]  #transform AG 2022-11 bag  [9.8,-34.3, 0.0]            # 地图到真实世界的平移
        # initialYawAngle: 10.0 # 247 #174 #184 #corridor:180 # 20 #43     20s:90                       # 初始偏航角
        # initialExtrinsicTrans: [0.5, 0.15, 0.0]   #a rather good initial guess [0.5, 0.15, 0.0]     # 初始位置猜测 （本应由WiFi给出）
        # mapYawAngle: -81.0 #0510                                                                      # 地图旋转角度


        # 机器人的初始姿态（平移 + 旋转） --- 在setInitialPose()中使用
        #  Setting estimate pose: Frame:map, Position(-6.76983, -8.49974, 0), Orientation(0, 0, 0.00181106, 0.999998) = Angle: 0.00362212
        # initialExtrinsicTrans: [-6.76983, -8.49974, 0.0]
        # initialYawAngle: 100.0

        # -------------------------------

    # Fujing's 0510 bag - 也即 seq01 在SIST1-D
        # for osmAG map, 这是拿来把mapPC_AG坐标系下的node点云转换到map_PC坐标系下，使他们有正确的坐标值
        # 对于确定的osmAG地图确定的root_node，以下参数无须改动 - root: lat = "31.17947960435" lon="121.59139728509
        # mapExtrinsicTrans: [9.8,-34.3, -16.0]
        mapYawAngle: -81.0 # seq01
        mapExtrinsicTrans: [9.8,-34.3, -16.0]
        # for Robot pose seq01 bag， 这些参数在 setInitialPose()中被使用
                    # seq01 - [0.5, 0.15, 0.0]  yaw:10.0
                    # 95 - [4.0, -6.0, 0.0] yaw: 90.0

        # 在开启全局定位下（模式1和模式2），以下可以是个垃圾值,因为不用他们了，但在仅位姿跟踪下则需要准确值
        # initialYawAngle: 90.0  # bag: 95, agile03_Mars_01
        # initialExtrinsicTrans: [4.0, -6.0, 0.0]  # bag: 95
        # initialExtrinsicTrans: [4.18, -3.51, 0.0]  # bag: agile03_Mars_01

        initialYawAngle: 180.0  # bag: agile03_corridor_01
        initialExtrinsicTrans: [8.36, 9.24, 0.0]  # bag: agile03_corridor_01

        # initialYawAngle: 0.0  # bag: agile03_corridor_02
        # initialExtrinsicTrans: [8.88, 19.31, 0.0]  # bag: agile03_corridor_02

        # initialYawAngle: 270.0  # bag: agile03_corridor_03
        # initialExtrinsicTrans: [7.95, 30.79, 0.0]  # bag: agile03_corridor_03

        # 是否全局定位+位姿跟踪（模式2）
        bRescueRobot: false                  # 是否启用全局定位 （true则同时需要启particle_generator节点）- (如果不启用则直接读入params中的位姿)

        # 25.2.20在测试这个模式-希望跑通 -- 2.28已跑通
        # 是否测试全局定位效果（模式1） --- 每帧点云都用来执行全局定位
        bTestRescue:  false               # 是否测试 [全局定位准不准] 模式
        # (全局定位ICP还不work)
        bInitializationWithICP: false
        root_long: 121.59139728509
        root_lat: 31.17947960435


        # 是否用多线程优化rescueRobot (多线程还不work)
        use_multithread: false

        # 添加particle_generator参数
        particle_generator_step: 2.0       # 粒子采样步长
        particle_generator_radius: 5.0     # 搜索半径(米)
        use_room_info: true               # 是否使用WiFiLocation中的room信息来过滤粒子
        boundary_filter_threshold: 0.5     # 边界过滤阈值（米），粒子距离边界小于此值时被过滤
        # without icp initialization
        rescue_angle_interval: 5.0
        # --------------------------------------------------------

        # ==================== AMCL-AGLoc融合算法参数 ====================
        # 主控制开关
        enable_amcl_mode: true             # 是否启用AMCL-AGLoc融合模式（false保持原有纯ICP模式）
        use_odometry_prediction: true      # 是否使用里程计预测（需要里程计话题）

        # 粒子滤波器参数
        amcl_min_particles: 100             # 最小粒子数
        amcl_max_particles: 5000            # 最大粒子数
        amcl_alpha_slow: 0.001              # 慢速权重平均衰减率（用于全局定位恢复）
        amcl_alpha_fast: 0.1                # 快速权重平均衰减率（用于全局定位恢复）
        amcl_resample_interval: 2           # 重采样间隔（每N帧执行一次重采样）

        # 运动模型参数（基于里程计的噪声模型）
        amcl_alpha1: 0.2                    # 旋转对旋转的噪声影响
        amcl_alpha2: 0.2                    # 平移对旋转的噪声影响
        amcl_alpha3: 0.2                    # 平移对平移的噪声影响
        amcl_alpha4: 0.2                    # 旋转对平移的噪声影响
        amcl_translation_threshold: 0.2     # 触发滤波器更新的最小平移距离(米)
        amcl_rotation_threshold: 0.2        # 触发滤波器更新的最小旋转角度(弧度)

        # 传感器模型参数（AGLoc特化的似然场模型）
        amcl_z_hit: 0.95                    # 正确测量的权重
        amcl_z_rand: 0.05                   # 随机测量的权重
        amcl_sigma_hit: 0.2                 # 测量噪声标准差(米)
        amcl_likelihood_max_dist: 2.0       # 似然场最大距离(米)

        # 重采样和收敛参数
        amcl_resample_threshold: 0.5        # 有效粒子数阈值（低于此值触发重采样）
        amcl_recovery_alpha_slow: 0.0       # 全局定位恢复的慢速阈值
        amcl_recovery_alpha_fast: 0.0       # 全局定位恢复的快速阈值
        amcl_random_injection_ratio: 0.1    # 随机粒子注入比例

        # AGLoc特定融合参数
        amcl_area_transition_threshold: 1.0 # 区域切换检测阈值(米)
        amcl_multi_hypothesis_threshold: 0.8 # 多假设检测阈值（权重比值）
        amcl_icp_refinement_enabled: true   # 是否对最优粒子进行ICP精化
        amcl_particle_spread_threshold: 2.0 # 粒子分布收敛阈值(米)

        # 调试和可视化参数
        amcl_publish_particles: true        # 是否发布粒子云用于可视化
        amcl_particle_publish_rate: 1.0     # 粒子云发布频率(Hz)
        amcl_enable_performance_logging: false # 是否启用性能日志
        # ================================================================

        opti: true
        ##LBFGS para
        mem_size: 8
        g_epsilon: 1.0e-3
        past: 3
        delta: 1.0e-3
        max_iterations : 10
        max_linesearch: 8
        min_step : 1.0e-20
        max_step: 1.0
        f_dec_coeff : 1.0e-4
        s_curv_coeff : 0.9
        cautious_factor: 1.0e-6
        machine_prec : 1.0e-16



        translation_thres: 0.01                # 平移阈值
        icp_iteration: 15 #10                  # 常规ICP迭代次数
        icp_init_iteration: 40 #180            # 初始化时ICP迭代次数
        icp_stop_translation_thred: 0.01       # ICP停止的平移阈值
        icp_stop_rotation_thred: 0.01 # 0.01   # ICP停止的旋转阈值
        # bool for using weight in icp
        use_weight: false                      # 是否使用权重



        # 走廊检测参数
        # 这些参数实现了论文III.G节"Corridorness Optimization"，在：
            # cloudHandler.cpp的mergeMapHistogram()函数中使用
            # 用于解决走廊环境中的特殊定位问题

        detect_corridor: false                 # 是否启用走廊检测
        maxPercentageCorridor: 1.0               # 最大走廊比例
        corridorDSmaxDist: 8.0                 # 走廊下采样最大距离
        # ---------------------------------------------------------


        # threshold to tell if icp is initialized or not, if not, err threshold need to be bigger and may need to recaluate intersection with map after reached a certain angle change
        initialized_thred: 20
        #after reach threshold, needs to recalculate intersection with map
        recalIntersectionThred: 1.5 #1.5

        # NOT VERY EFFECTIVE
        percentageThred: 0.12
        averDistanceThred: 0.15
        radiusDisthred: 0.1
        # ----------------------------------------------------

        # 环境过滤参数
        # 这些参数在cloudBase.cpp的organizePointcloud()中使用，用于：
            # 过滤掉地面和天花板的点云

        groundThred:   -0.3                    # 地面点过滤阈值
        ceilingThred:   2.0 #1.8               # 天花板点过滤阈值
        parallelThred: 15.0                    # 平行线判定阈值

        subSample: 5
        # only for check initialization status...
        pause_iter: false
        initialization_imu: false
        diff_angle_init: 150.0





        bOnlyScoreParticles: false
        # 控制体素滤波器的降采样程度，若要减小降采样程度 - 保留更多点，则需要减小scoreDownsampleRate的值
        scoreDownsampleRate: 0.1

        # only for testing
        bResultChecking: false
        checkingAngle: 174.0
        checkingGuessX: 7.25
        checkingGuessY: 35.25

        bCHENGNODE: true
        bGenerateResultFile: false
        bFurthestRingTracking: true
        # set to 0 means don't pause
        turkeyPauseThred: 0.0

        # both false, check passage and whole map, for paper experiments
        bAllPassageOpen: false
        bAllPassageClose: false

